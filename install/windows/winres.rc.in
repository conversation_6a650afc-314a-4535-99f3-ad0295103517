#include "verrsrc.h"

IDI_ICON1               ICON        "@windows_icon@"


#define VER_FILEVERSION             @PROJECT_VERSION_MAJOR@,@PROJECT_VERSION_MINOR@,@PROJECT_VERSION_PATCH@
#define VER_FILEVERSION_STR         "@PROJECT_VERSION_MAJOR@.@PROJECT_VERSION_MINOR@.@PROJECT_VERSION_PATCH@\0"

#define VER_PRODUCTVERSION          @PROJECT_VERSION_MAJOR@,@PROJECT_VERSION_MINOR@,@PROJECT_VERSION_PATCH@
#define VER_PRODUCTVERSION_STR     	"@PROJECT_VERSION_MAJOR@.@PROJECT_VERSION_MINOR@.@PROJECT_VERSION_PATCH@\0"

1 VERSIONINFO
 FILEVERSION VER_FILEVERSION
 PRODUCTVERSION VER_PRODUCTVERSION
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS VOS_NT_WINDOWS32
 FILETYPE VFT_APP
 FILESUBTYPE 0x0L
 BEGIN
	BLOCK "StringFileInfo"
	BEGIN
		BLOCK "040904e4"
		BEGIN
			VALUE "CompanyName", "3T Software Labs Ltd\0"
			VALUE "FileDescription", "Robo 3T, MongoDB management tool\0"
			VALUE "FileVersion", VER_FILEVERSION_STR
			VALUE "LegalCopyright", "Copyright 3T Software Labs Ltd, 2014 - 2017\0"
			VALUE "ProductName", "Robo 3T\0"
			VALUE "ProductVersion", VER_PRODUCTVERSION_STR
			VALUE "OriginalFilename", "robo3t.exe\0"
			VALUE "InternalName", "Robo 3T\0"
		END
	END
	BLOCK "VarFileInfo"
	BEGIN
		/* The following line should only be modified for localized versions.     */
		/* It consists of any number of WORD,WORD pairs, with each pair           */
		/* describing a language,codepage combination supported by the file.      */
		/*                                                                        */
		/* For example, a file might have values "0x409,1252" indicating that it  */
		/* supports English language (0x409) in the Windows ANSI codepage (1252). */

		VALUE "Translation", 0x409, 1252
	END
 END
