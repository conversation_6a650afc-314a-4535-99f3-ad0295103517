      * Copyright (c) 2015 <PERSON> Mon<PERSON>, D+H <<EMAIL>>
      * All rights reserved.
      *
      * Redistribution and use in source and binary forms,
      * with or without modification, are permitted provided
      * that the following conditions are met:
      *
      *   Redistributions of source code must retain the above
      *   copyright notice, this list of conditions and the
      *   following disclaimer.
      *
      *   Redistributions in binary form must reproduce the above
      *   copyright notice, this list of conditions and the following
      *   disclaimer in the documentation and/or other materials
      *   provided with the distribution.
      *
      *   Neither the name of the copyright holder nor the names
      *   of any other contributors may be used to endorse or
      *   promote products derived from this software without
      *   specific prior written permission.
      *
      * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND
      * CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES,
      * INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
      * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
      * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
      * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
      * SPECIAL, EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING,
      * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
      * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
      * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
      * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
      * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE
      * USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
      * OF SUCH DAMAGE.

      /if not defined(LIBSSH2_CCSID_H_)
      /define LIBSSH2_CCSID_H_

      /include "libssh2rpg/libssh2"

     d libssh2_from_ccsid...
     d                 pr              *   extproc('libssh2_from_ccsid')        char *
     d  session                        *   value                                LIBSSH2_SESSION *
     d  cache                          *                                        libssh2_string_cache
     d                                                                          *(*)
     d  ccsid                              value like(libssh2_Cushort)
     d  string                         *   value options(*string)               const char *
     d  inlen                              value like(libssh2_Cssize_t)
     d  outlen                             like(libssh2_Csize_t) options(*omit)

     d libssh2_to_ccsid...
     d                 pr              *   extproc('libssh2_to_ccsid')          char *
     d  session                        *   value                                LIBSSH2_SESSION *
     d  cache                          *                                        libssh2_string_cache
     d                                                                          *(*)
     d  ccsid                              value like(libssh2_Cushort)
     d  string                         *   value options(*string)               const char *
     d  inlen                              value like(libssh2_Cssize_t)
     d  outlen                             like(libssh2_Csize_t) options(*omit)

     d libssh2_release_string_cache...
     d                 pr                  extproc(
     d                                     'libssh2_release_string_cache')
     d  session                        *   value                                LIBSSH2_SESSION *
     d  cache                          *                                        libssh2_string_cache
     d                                                                          *(*)

      /endif                                                                    LIBSSH2_CCSID_H_
