.\"
.\" Copyright (c) 2009 by <PERSON>
.\"
.TH libssh2_knownhost_del 3 "28 May 2009" "libssh2 1.2" "libssh2 manual"
.SH NAME
libssh2_knownhost_del - delete a known host entry
.SH SYNOPSIS
#include <libssh2.h>

int libssh2_knownhost_del(LIBSSH2_KNOWNHOSTS *hosts,
                          struct libssh2_knownhost *entry);
.SH DESCRIPTION
Delete a known host entry from the collection of known hosts.

\fIentry\fP is a pointer to a struct that you can extract with
\fIlibssh2_knownhost_check(3)\fP or \fIlibssh2_knownhost_get(3)\fP.
.SH RETURN VALUE
Returns a regular libssh2 error code, where negative values are error codes
and 0 indicates success.
.SH AVAILABILITY
Added in libssh2 1.2
.SH SEE ALSO
.BR libssh2_knownhost_init(3)
.BR libssh2_knownhost_free(3)
.BR libssh2_knownhost_add(3)
.BR libssh2_knownhost_check(3)
