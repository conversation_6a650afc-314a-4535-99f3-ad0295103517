.\"
.\" Copyright (c) 2009 by <PERSON><PERSON>
.\"
.TH libssh2_agent_disconnect 3 "23 Dec 2009" "libssh2 1.2" "libssh2 manual"
.SH NAME
libssh2_agent_disconnect - close a connection to an ssh-agent
.SH SYNOPSIS
#include <libssh2.h>

int libssh2_agent_disconnect(LIBSSH2_AGENT *agent);
.SH DESCRIPTION
Close a connection to an ssh-agent.

.SH RETURN VALUE
Returns 0 if succeeded, or a negative value for error.
.SH AVAILABILITY
Added in libssh2 1.2
.SH SEE ALSO
.BR libssh2_agent_connect(3)
.BR libssh2_agent_free(3)
