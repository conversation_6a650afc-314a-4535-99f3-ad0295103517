.TH libssh2_sftp_seek64 3 "22 Dec 2008" "libssh2 1.0" "libssh2 manual"
.SH NAME
libssh2_sftp_seek64 - set the read/write position within a file
.SH SYNOPSIS
.nf
#include <libssh2.h>
#include <libssh2_sftp.h>

void libssh2_sftp_seek64(LIBSSH2_SFTP_HANDLE *handle,
                         libssh2_uint64_t offset);
.SH DESCRIPTION
\fIhandle\fP - SFTP File Handle as returned by 
.BR libssh2_sftp_open_ex(3)

\fIoffset\fP - Number of bytes from the beginning of file to seek to.

Move the file handle's internal pointer to an arbitrary location. libssh2
implements file pointers as a localized concept to make file access appear
more POSIX like. No packets are exchanged with the server during a seek
operation. The localized file pointer is simply used as a convenience offset
during read/write operations.

You MUST NOT seek during writing or reading a file with SFTP, as the internals
use outstanding packets and changing the "file position" during transit will
results in badness.
.SH AVAILABILITY
Added in 1.0
.SH SEE ALSO
.BR libssh2_sftp_open_ex(3)
