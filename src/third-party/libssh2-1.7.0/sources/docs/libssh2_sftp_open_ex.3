.TH libssh2_sftp_open_ex 3 "1 Jun 2007" "libssh2 0.15" "libssh2 manual"
.SH NAME
libssh2_sftp_open - open filehandle for file on SFTP.
.SH SYNOPSIS
.nf
#include <libssh2.h>
#include <libssh2_sftp.h>

LIBSSH2_SFTP_HANDLE *
libssh2_sftp_open_ex(LIBSSH2_SFTP *sftp, const char *filename,
                     unsigned int filename_len, unsigned long flags,
                     long mode, int open_type);
.SH DESCRIPTION
\fIsftp\fP - SFTP instance as returned by \fIlibssh2_sftp_init(3)\fP

\fIfilename\fP - Remote file/directory resource to open 

\fIfilename_len\fP - Length of filename 

\fIflags\fP - Any reasonable combination of the LIBSSH2_FXF_* constants:
.RS
.IP LIBSSH2_FXF_READ
Open the file for reading.
.IP LIBSSH2_FXF_WRITE
Open the file for writing.  If both this and LIBSSH2_FXF_READ are specified,
the file is opened for both reading and writing.
.IP LIBSSH2_FXF_APPEND
Force all writes to append data at the end of the file.
.IP LIBSSH2_FXF_CREAT,
If this flag is specified, then a new file will be created if one does not
already exist (if LIBSSH2_FXF_TRUNC is specified, the new file will be
truncated to zero length if it previously exists)
.IP LIBSSH2_FXF_TRUNC
Forces an existing file with the same name to be truncated to zero length when
creating a file by specifying LIBSSH2_FXF_CREAT. LIBSSH2_FXF_CREAT MUST also
be specified if this flag is used.
.IP LIBSSH2_FXF_EXCL
Causes the request to fail if the named file already exists.
LIBSSH2_FXF_CREAT MUST also be specified if this flag is used.

.RE
\fImode\fP - POSIX file permissions to assign if the file is being newly
created. See the LIBSSH2_SFTP_S_* convenience defines in <libssh2_sftp.h>

\fIopen_type\fP - Either of LIBSSH2_SFTP_OPENFILE (to open a file) or
LIBSSH2_SFTP_OPENDIR (to open a directory).
.SH RETURN VALUE
A pointer to the newly created LIBSSH2_SFTP_HANDLE instance or NULL on
failure.
.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP -  An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_SOCKET_TIMEOUT\fP - 

\fILIBSSH2_ERROR_SFTP_PROTOCOL\fP - An invalid SFTP protocol response was 
received on the socket, or an SFTP operation caused an errorcode to be 
returned by the server.

\fILIBSSH2_ERROR_EAGAIN\fP - Marked for non-blocking I/O but the call would
block.
.SH SEE ALSO
.BR libssh2_sftp_close_handle(3)

