.TH libssh2_sftp_realpath 3 "20 Feb 2010" "libssh2 1.2.4" "libssh2 manual"
.SH NAME
libssh2_sftp_realpath - convenience macro for \fIlibssh2_sftp_symlink_ex(3)\fP
.SH SYNOPSIS
.nf
#include <libssh2.h>

#define libssh2_sftp_realpath(sftp, path, target, maxlen) \\
  libssh2_sftp_symlink_ex((sftp), (path), strlen(path), (target), (maxlen), \\
                          LIBSSH2_SFTP_REALPATH)
.SH DESCRIPTION
This is a macro defined in a public libssh2 header file that is using the
underlying function \fIlibssh2_sftp_symlink_ex(3)\fP.
.SH RETURN VALUE
See \fIlibssh2_sftp_symlink_ex(3)\fP
.SH ERRORS
See \fIlibssh2_sftp_symlink_ex(3)\fP
.SH SEE ALSO
.BR libssh2_sftp_symlink_ex(3)
