.TH libssh2_sftp_unlink_ex 3 "1 Jun 2007" "libssh2 0.15" "libssh2 manual"
.SH NAME
libssh2_sftp_unlink_ex - unlink an SFTP file
.SH SYNOPSIS
#include <libssh2.h>
#include <libssh2_sftp.h>

int 
libssh2_sftp_unlink_ex(LIBSSH2_SFTP *sftp, const char *filename, unsigned int filename_len);

int 
libssh2_sftp_unlink(LIBSSH2_SFTP *sftp, const char *filename);

.SH DESCRIPTION
\fIsftp\fP - SFTP instance as returned by 
.BR libssh2_sftp_init(3)

\fIfilename\fP - Path and name of the existing filesystem entry

\fIfilename_len\fP - Length of the path and name of the existing 
filesystem entry

Unlink (delete) a file from the remote filesystem.

.SH RETURN VALUE
Return 0 on success or negative on failure.  It returns
LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it isn't really a failure per se.

.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP -  An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_SOCKET_TIMEOUT\fP - 

\fILIBSSH2_ERROR_SFTP_PROTOCOL\fP - An invalid SFTP protocol response was 
received on the socket, or an SFTP operation caused an errorcode to 
be returned by the server.

.SH SEE ALSO
.BR libssh2_sftp_init(3)
