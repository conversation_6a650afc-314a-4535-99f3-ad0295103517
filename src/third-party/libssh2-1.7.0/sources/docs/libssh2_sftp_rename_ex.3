.TH libssh2_sftp_rename_ex 3 "1 Jun 2007" "libssh2 0.15" "libssh2 manual"
.SH NAME
libssh2_sftp_rename_ex - rename an SFTP file
.SH SYNOPSIS
#include <libssh2.h>
#include <libssh2_sftp.h>

int 
libssh2_sftp_rename_ex(LIBSSH2_SFTP *sftp, const char *source_filename, unsigned int source_filename_len, const char *dest_filename, unsigned int dest_filename_len, long flags);

int 
libssh2_sftp_rename_ex(LIBSSH2_SFTP *sftp, const char *source_filename, const char *dest_filename);

.SH DESCRIPTION
\fIsftp\fP - SFTP instance as returned by 
.BR libssh2_sftp_init(3)

\fIsourcefile\fP - Path and name of the existing filesystem entry

\fIsourcefile_len\fP - Length of the path and name of the existing 
filesystem entry

\fIdestfile\fP - Path and name of the target filesystem entry

\fIdestfile_len\fP - Length of the path and name of the target 
filesystem entry

\fIflags\fP - 
Bitmask flags made up of LIBSSH2_SFTP_RENAME_* constants.

Rename a filesystem object on the remote filesystem. The semantics of 
this command typically include the ability to move a filesystem object 
between folders and/or filesystem mounts. If the LIBSSH2_SFTP_RENAME_OVERWRITE 
flag is not set and the destfile entry already exists, the operation 
will fail. Use of the other two flags indicate a preference (but not a 
requirement) for the remote end to perform an atomic rename operation 
and/or using native system calls when possible.

.SH RETURN VALUE
Return 0 on success or negative on failure.  It returns
LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it isn't really a failure per se.

.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP -  An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_SOCKET_TIMEOUT\fP - 

\fILIBSSH2_ERROR_SFTP_PROTOCOL\fP - An invalid SFTP protocol response was 
received on the socket, or an SFTP operation caused an errorcode to 
be returned by the server.

.SH SEE ALSO
.BR libssh2_sftp_init(3)
