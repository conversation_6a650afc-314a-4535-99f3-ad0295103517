.\"
.\" Copyright (c) 2009 by <PERSON><PERSON>
.\"
.TH libssh2_agent_init 3 "23 Dec 2009" "libssh2 1.2" "libssh2 manual"
.SH NAME
libssh2_agent_init - init an ssh-agent handle
.SH SYNOPSIS
#include <libssh2.h>

LIBSSH2_AGENT *libssh2_agent_init(LIBSSH2_SESSION *session);
.SH DESCRIPTION
Init an ssh-agent handle. Returns the handle to an internal
representation of an ssh-agent connection.  After the successful
initialization, an application can call \fBlibssh2_agent_connect(3)\fP
to connect to a running ssh-agent.

Call \fBlibssh2_agent_free(3)\fP to free the handle again after you're
doing using it.
.SH RETURN VALUE
Returns a handle pointer or NULL if something went wrong. The returned handle
is used as input to all other ssh-agent related functions libssh2 provides.
.SH AVAILABILITY
Added in libssh2 1.2
.SH SEE ALSO
.BR libssh2_agent_connect(3)
.BR libssh2_agent_free(3)
