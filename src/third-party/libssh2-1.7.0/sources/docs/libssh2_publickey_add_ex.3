.TH libssh2_publickey_add_ex 3 "1 Jun 2007" "libssh2 0.15" "libssh2 manual"
.SH NAME
libssh2_publickey_add_ex - Add a public key entry
.SH SYNOPSIS
#include <libssh2.h>

int
libssh2_publickey_add_ex(LIBSSH2_PUBLICKEY *pkey, const unsigned char *name,
                         unsigned long name_len, const unsigned char *blob,
                         unsigned long blob_len, char overwrite,
                         unsigned long num_attrs,
                         const libssh2_publickey_attribute attrs[])

.SH DESCRIPTION
TBD
.SH RETURN VALUE
Returns 0 on success, negative on failure.
.SH ERRORS
LIBSSH2_ERROR_BAD_USE
LIBSSH2_ERROR_ALLOC,
LIBSSH2_ERROR_EAGAIN
LIBSSH2_ERROR_SOCKET_SEND,
LIBSSH2_ERROR_SOCKET_TIMEOUT,
LIBSSH2_ERROR_PUBLICKEY_PROTOCOL,
.SH SEE ALSO
