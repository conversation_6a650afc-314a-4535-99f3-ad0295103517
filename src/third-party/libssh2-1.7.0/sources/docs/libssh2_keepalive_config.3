.TH libssh2_keepalive_config 3 "12 Apr 2011" "libssh2 1.2.5" "libssh2 manual"
.SH NAME
libssh2_keepalive_config - short function description
.SH SYNOPSIS
#include <libssh2.h>
.nf

void libssh2_keepalive_config(LIBSSH2_SESSION *session,
                              int want_reply,
                              unsigned interval);
.fi
.SH DESCRIPTION
Set how often keepalive messages should be sent. \fBwant_reply\fP indicates
whether the keepalive messages should request a response from the server.
\fBinterval\fP is number of seconds that can pass without any I/O, use 0 (the
default) to disable keepalives.  To avoid some busy-loop corner-cases, if you
specify an interval of 1 it will be treated as 2.

Note that non-blocking applications are responsible for sending the keepalive
messages using \fBlibssh2_keepalive_send(3)\fP.
.SH RETURN VALUE
Nothing
.SH AVAILABILITY
Added in libssh2 1.2.5
.SH SEE ALSO
.BR libssh2_keepalive_send(3)

