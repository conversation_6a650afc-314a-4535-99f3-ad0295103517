.TH libssh2_channel_window_read_ex 3 "1 Jun 2007" "libssh2 0.15" "libssh2 manual"
.SH NAME
libssh2_channel_window_read_ex - Check the status of the read window
.SH SYNOPSIS
#include <libssh2.h>

unsigned long
libssh2_channel_window_read_ex(LIBSSH2_CHANNEL *channel,
                               unsigned long *read_avail,
                               unsigned long *window_size_initial)
.SH DESCRIPTION
Check the status of the read window. Returns the number of bytes which the
remote end may send without overflowing the window limit read_avail (if
passed) will be populated with the number of bytes actually available to be
read window_size_initial (if passed) will be populated with the
window_size_initial as defined by the channel_open request
.SH RETURN VALUE
The number of bytes which the remote end may send without overflowing the
window limit
.SH ERRORS

.SH SEE ALSO
.BR libssh2_channel_receive_window_adjust(3),
.BR libssh2_channel_window_write_ex(3)
