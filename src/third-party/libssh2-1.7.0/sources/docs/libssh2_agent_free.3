.\"
.\" Copyright (c) 2009 by <PERSON><PERSON>
.\"
.TH libssh2_agent_free 3 "28 May 2009" "libssh2 1.2" "libssh2 manual"
.SH NAME
libssh2_agent_free - free an ssh-agent handle
.SH SYNOPSIS
#include <libssh2.h>

void libssh2_agent_free(LIBSSH2_AGENT *agent);
.SH DESCRIPTION
Free an ssh-agent handle.  This function also frees the internal
collection of public keys.
.SH RETURN VALUE
None.
.SH AVAILABILITY
Added in libssh2 1.2
.SH SEE ALSO
.BR libssh2_agent_init(3)
.BR libssh2_agent_disconnect(3)
