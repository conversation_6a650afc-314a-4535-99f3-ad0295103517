.TH libssh2_channel_setenv_ex 3 "1 Jun 2007" "libssh2 0.15" "libssh2 manual"
.SH NAME
libssh2_channel_setenv_ex - set an environment variable on the channel
.SH SYNOPSIS
#include <libssh2.h>

int
libssh2_channel_setenv_ex(LIBSSH2_CHANNEL *channel, char *varname, unsigned int varname_len, const char *value, unsigned int value_len);

int
libssh2_channel_setenv(LIBSSH2_CHANNEL *channel, char *varname, const char *value);

.SH DESCRIPTION
\fIchannel\fP - Previously opened channel instance such as returned by 
.BR libssh2_channel_open_ex(3)

\fIvarname\fP - Name of environment variable to set on the remote 
channel instance.

\fIvarname_len\fP - Length of passed varname parameter.

\fIvalue\fP - Value to set varname to.

\fIvalue_len\fP - Length of value parameter.

Set an environment variable in the remote channel's process space. Note that
this does not make sense for all channel types and may be ignored by the
server despite returning success.
.SH RETURN VALUE
Return 0 on success or negative on failure.  It returns
LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it isn't really a failure per se.

.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP -  An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_CHANNEL_REQUEST_DENIED\fP - 
.SH SEE ALSO
.BR libssh2_channel_open_ex(3)
