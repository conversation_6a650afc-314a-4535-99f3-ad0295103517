.TH libssh2_session_set_timeout 3 "4 May 2011" "libssh2 1.2.9" "libssh2 manual"
.SH NAME
libssh2_session_set_timeout - set timeout for blocking functions
.SH SYNOPSIS
#include <libssh2.h>
.nf
void libssh2_session_set_timeout(LIBSSH2_SESSION *session, long timeout);
.SH DESCRIPTION
Set the \fBtimeout\fP in milliseconds for how long a blocking the libssh2
function calls may wait until they consider the situation an error and return
LIBSSH2_ERROR_TIMEOUT.

By default or if you set the timeout to zero, libssh2 has no timeout for
blocking functions.
.SH RETURN VALUE
Nothing
.SH AVAILABILITY
Added in 1.2.9
.SH SEE ALSO
.BR libssh2_session_get_timeout(3)
