.TH libssh2_channel_set_blocking 3 "1 Jun 2007" "libssh2 0.15" "libssh2 manual"
.SH NAME
libssh2_channel_set_blocking - set or clear blocking mode on channel
.SH SYNOPSIS
#include <libssh2.h>

void 
libssh2_channel_set_blocking(LIBSSH2_CHANNEL *channel, int blocking);
.SH DESCRIPTION
\fIchannel\fP - channel stream to set or clean blocking status on.

\fIblocking\fP - Set to a non-zero value to make the channel block, or zero to
make it non-blocking.

Currently this is just a short cut call to 
.BR libssh2_session_set_blocking(3)
and therefore will affect the session and all channels.
.SH RETURN VALUE
None
.SH SEE ALSO
.BR libssh2_session_set_blocking(3)
.BR libssh2_channel_read_ex(3)
.BR libssh2_channel_write_ex(3)
