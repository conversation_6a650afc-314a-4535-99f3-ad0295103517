.TH libssh2_channel_eof 3 "1 Jun 2007" "libssh2 0.15" "libssh2 manual"
.SH NAME
libssh2_channel_eof - check a channel's EOF status
.SH SYNOPSIS
#include <libssh2.h>

int 
libssh2_channel_eof(LIBSSH2_CHANNEL *channel);
.SH DESCRIPTION
\fIchannel\fP - active channel stream to set closed status on.

Check if the remote host has sent an EOF status for the selected stream.
.SH RETURN VALUE
Returns 1 if the remote host has sent EOF, otherwise 0. Negative on
failure.
.SH SEE ALSO
.BR libssh2_channel_close(3)
