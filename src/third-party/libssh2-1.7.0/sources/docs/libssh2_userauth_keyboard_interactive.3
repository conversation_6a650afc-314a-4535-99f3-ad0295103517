.TH libssh2_userauth_keyboard_interactive 3 "20 Feb 2010" "libssh2 1.2.4" "libssh2 manual"
.SH NAME
libssh2_userauth_keyboard_interactive - convenience macro for \fIlibssh2_userauth_keyboard_interactive_ex(3)\fP calls
.SH SYNOPSIS
#include <libssh2.h>
.nf

int
libssh2_userauth_keyboard_interactive(LIBSSH2_SESSION* session,
                                      const char *username,
           LIBSSH2_USERAUTH_KBDINT_RESPONSE_FUNC((*response_callback)));
.SH DESCRIPTION
This is a macro defined in a public libssh2 header file that is using the
underlying function \fIlibssh2_userauth_keyboard_interactive_ex(3)\fP.
.SH RETURN VALUE
See \fIlibssh2_userauth_keyboard_interactive_ex(3)\fP
.SH ERRORS
See \fIlibssh2_userauth_keyboard_interactive_ex(3)\fP
.SH SEE ALSO
.BR libssh2_userauth_keyboard_interactive_ex(3)
