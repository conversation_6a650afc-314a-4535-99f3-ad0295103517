.TH libssh2_sftp_shutdown 3 "1 Jun 2007" "libssh2 0.15" "libssh2 manual"
.SH NAME
libssh2_sftp_shutdown - shut down an SFTP session
.SH SYNOPSIS
#include <libssh2.h>
#include <libssh2_sftp.h>

int 
libssh2_sftp_shutdown(LIBSSH2_SFTP *sftp);

.SH DESCRIPTION
\fIsftp\fP - SFTP instance as returned by 
.BR libssh2_sftp_init(3)

Destroys a previously initialized SFTP session and frees all resources 
associated with it.

.SH RETURN VALUE
Return 0 on success or negative on failure.  It returns
LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it isn't really a failure per se.

.SH SEE ALSO
.BR libssh2_sftp_init(3)
