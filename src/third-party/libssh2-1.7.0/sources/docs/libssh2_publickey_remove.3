.TH libssh2_publickey_remove 3 "20 Feb 2010" "libssh2 1.2.4" "libssh2 manual"
.SH NAME
libssh2_publickey_remove - convenience macro for \fIlibssh2_publickey_remove_ex(3)\fP calls
.SH SYNOPSIS
#include <libssh2.h>

int libssh2_publickey_remove(LIBSSH2_PUBLICKEY *pkey,
			     const unsigned char *name, unsigned long name_len,
			     const unsigned char *blob, unsigned long blob_len);

.SH DESCRIPTION
This is a macro defined in a public libssh2 header file that is using the
underlying function \fIlibssh2_publickey_remove_ex(3)\fP.
.SH RETURN VALUE
See \fIlibssh2_publickey_remove_ex(3)\fP
.SH ERRORS
See \fIlibssh2_publickey_remove_ex(3)\fP
.SH SEE ALSO
.BR libssh2_publickey_remove_ex(3)
