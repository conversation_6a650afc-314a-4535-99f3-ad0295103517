.TH libssh2_sftp_seek 3 "22 Dec 2008" "libssh2 1.0" "libssh2 manual"
.SH NAME
libssh2_sftp_seek - set the read/write position indicator within a file
.SH SYNOPSIS
.nf
#include <libssh2.h>
#include <libssh2_sftp.h>

void libssh2_sftp_seek(LIBSSH2_SFTP_HANDLE *handle, size_t offset);
.SH DESCRIPTION
Deprecated function. Use \fIlibssh2_sftp_seek64(3)\fP instead!

\fIhandle\fP - SFTP File Handle as returned by 
.BR libssh2_sftp_open_ex(3)

\fIoffset\fP - Number of bytes from the beginning of file to seek to.

Move the file handle's internal pointer to an arbitrary location. 
Note that libssh2 implements file pointers as a localized concept to make 
file access appear more POSIX like. No packets are exchanged with the server 
during a seek operation. The localized file pointer is simply used as a 
convenience offset during read/write operations.
.SH SEE ALSO
.BR libssh2_sftp_open_ex(3),
.BR libssh2_sftp_seek64(3)
