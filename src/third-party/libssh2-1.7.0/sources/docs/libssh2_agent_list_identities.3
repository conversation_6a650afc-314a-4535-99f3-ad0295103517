.\"
.\" Copyright (c) 2009 by <PERSON><PERSON>
.\"
.TH libssh2_agent_list_identities 3 "23 Dec 2009" "libssh2 1.2" "libssh2 manual"
.SH NAME
libssh2_agent_list_identities - request an ssh-agent to list of public keys.
.SH SYNOPSIS
#include <libssh2.h>

int libssh2_agent_list_identities(LIBSSH2_AGENT *agent);
.SH DESCRIPTION
Request an ssh-agent to list of public keys, and stores them in the
internal collection of the handle.  Call
\fIlibssh2_agent_get_identity(3)\fP to get a public key off the
collection.

.SH RETURN VALUE
Returns 0 if succeeded, or a negative value for error.
.SH AVAILABILITY
Added in libssh2 1.2
.SH SEE ALSO
.BR libssh2_agent_connect(3)
.BR libssh2_agent_get_identity(3)

