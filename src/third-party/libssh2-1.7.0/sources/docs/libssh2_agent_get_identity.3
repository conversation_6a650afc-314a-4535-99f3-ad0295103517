.\"
.\" Copyright (c) 2009 by <PERSON><PERSON>
.\"
.TH libssh2_agent_get_identity 3 "23 Dec 2009" "libssh2 1.2" "libssh2 manual"
.SH NAME
libssh2_agent_get_identity - get a public key off the collection of public keys managed by ssh-agent
.SH SYNOPSIS
#include <libssh2.h>

int libssh2_agent_get_identity(LIBSSH2_AGENT *agent,
                               struct libssh2_agent_publickey **store,
                               struct libssh2_agent_publickey *prev);
.SH DESCRIPTION
\fIlibssh2_agent_get_identity(3)\fP allows an application to iterate
over all public keys in the collection managed by ssh-agent.

\fIstore\fP should point to a pointer that gets filled in to point to the
public key data.

\fIprev\fP is a pointer to a previous 'struct libssh2_agent_publickey'
as returned by a previous invoke of this function, or NULL to get the
first entry in the internal collection.
.SH RETURN VALUE
Returns 0 if everything is fine and information about a host was stored in
the \fIstore\fP struct.

Returns 1 if it reached the end of public keys.

Returns negative values for error
.SH AVAILABILITY
Added in libssh2 1.2
.SH SEE ALSO
.BR libssh2_agent_list_identities(3)
.BR libssh2_agent_userauth(3)
