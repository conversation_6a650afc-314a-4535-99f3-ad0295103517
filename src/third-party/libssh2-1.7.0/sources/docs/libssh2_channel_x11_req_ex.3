.TH libssh2_channel_x11_req_ex 3 "1 Jun 2007" "libssh2 0.15" "libssh2 manual"
.SH NAME
libssh2_channel_x11_req_ex - request an X11 forwarding channel
.SH SYNOPSIS
#include <libssh2.h>

int
libssh2_channel_x11_req_ex(LIBSSH2_CHANNEL *channel, int single_connection, const char *auth_proto, const char *auth_cookie, int screen_number);

int
libssh2_channel_x11_req(LIBSSH2_CHANNEL *channel, int screen_number);

.SH DESCRIPTION
\fIchannel\fP - Previously opened channel instance such as returned by 
.BR libssh2_channel_open_ex(3)

\fIsingle_connection\fP - non-zero to only forward a single connection.

\fIauth_proto\fP - X11 authentication protocol to use

\fIauth_cookie\fP - the cookie (hexadecimal encoded).

\fIscreen_number\fP - the XLL screen to forward

Request an X11 forwarding on \fIchannel\fP. To use X11 forwarding, 
.BR libssh2_session_callback_set(3)
must first be called to set \fBLIBSSH2_CALLBACK_X11\fP. This callback will be
invoked when the remote host accepts the X11 forwarding.

.SH RETURN VALUE
Return 0 on success or negative on failure.  It returns
LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it isn't really a failure per se.

.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP -  An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_CHANNEL_REQUEST_DENIED\fP - 

.SH SEE ALSO
.BR libssh2_channel_open_ex(3)
.BR libssh2_session_callback_set(3)
