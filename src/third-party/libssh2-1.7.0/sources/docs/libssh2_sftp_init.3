.TH libssh2_sftp_init 3 "1 Jun 2007" "libssh2 0.15" "libssh2 manual"
.SH NAME
libssh2_sftp_init - open SFTP channel for the given SSH session.
.SH SYNOPSIS
#include <libssh2.h>
#include <libssh2_sftp.h>

LIBSSH2_SFTP *
libssh2_sftp_init(LIBSSH2_SESSION *session);

.SH DESCRIPTION
\fIsession\fP - Session instance as returned by 
.BR libssh2_session_init_ex(3)

Open a channel and initialize the SFTP subsystem. Although the SFTP subsystem
operates over the same type of channel as those exported by the Channel API,
the protocol itself implements its own unique binary packet protocol which
must be managed with the libssh2_sftp_*() family of functions. When an SFTP
session is complete, it must be destroyed using the
.BR libssh2_sftp_shutdown(3)
function.
.SH RETURN VALUE
A pointer to the newly allocated SFTP instance or NULL on failure.
.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP -  An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_SOCKET_TIMEOUT\fP - 

\fILIBSSH2_ERROR_SFTP_PROTOCOL\fP - An invalid SFTP protocol response was 
received on the socket, or an SFTP operation caused an errorcode to be 
returned by the server.

\fILIBSSH2_ERROR_EAGAIN\fP - Marked for non-blocking I/O but the call would
block.
.SH SEE ALSO
.BR libssh2_sftp_shutdown(3)
.BR libssh2_sftp_open_ex(3)
