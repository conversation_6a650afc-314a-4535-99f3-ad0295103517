.TH libssh2_sftp_fsync 3 "8 Apr 2013" "libssh2 1.4.4" "libssh2 manual"
.SH NAME
libssh2_sftp_fsync - synchronize file to disk
.SH SYNOPSIS
.nf
#include <libssh2.h>
#include <libssh2_sftp.h>

int
libssh2_sftp_fsync(LIBSSH2_SFTP_HANDLE *handle)
.fi
.SH DESCRIPTION
This function causes the remote server to synchronize the file
data and metadata to disk (like fsync(2)).

For this to <NAME_EMAIL> support on the server.

\fIhandle\fP - SFTP File Handle as returned by
.BR libssh2_sftp_open_ex(3)

.SH RETURN VALUE
Returns 0 on success or negative on failure. If used in non-blocking mode, it
returns LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it isn't really a failure per se.
.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP -  An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_SFTP_PROTOCOL\fP - An invalid SFTP protocol response
was received on the socket, or an SFTP operation caused an errorcode
to be returned by the server.  In particular, this can be returned if
the SSH server does not support the fsync operation: the SFTP subcode
\fILIBSSH2_FX_OP_UNSUPPORTED\fP will be returned in this case.

.SH AVAILABILITY
Added in libssh2 1.4.4 and OpenSSH 6.3.
.SH SEE ALSO
.BR fsync(2)
