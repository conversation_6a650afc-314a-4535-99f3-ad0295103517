.TH libssh2_sftp_symlink 3 "20 Feb 2010" "libssh2 1.2.4" "libssh2 manual"
.SH NAME
libssh2_sftp_symlink - convenience macro for \fIlibssh2_sftp_symlink_ex(3)\fP
.SH SYNOPSIS
.nf
#include <libssh2.h>

#define libssh2_sftp_symlink(sftp, orig, linkpath) \\
    libssh2_sftp_symlink_ex((sftp), (orig), strlen(orig), (linkpath), \\
                            strlen(linkpath), LIBSSH2_SFTP_SYMLINK)
.SH DESCRIPTION
This is a macro defined in a public libssh2 header file that is using the
underlying function \fIlibssh2_sftp_symlink_ex(3)\fP.
.SH RETURN VALUE
See \fIlibssh2_sftp_symlink_ex(3)\fP
.SH ERRORS
See \fIlibssh2_sftp_symlink_ex(3)\fP
.SH SEE ALSO
.BR libssh2_sftp_symlink_ex(3)
