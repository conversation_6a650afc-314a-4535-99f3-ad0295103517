.TH libssh2_userauth_keyboard_interactive_ex 3 "8 Mar 2008" "libssh2 0.19" "libssh2 manual"
.SH NAME
libssh2_userauth_keyboard_interactive_ex - authenticate a session using
keyboard-interactive authentication
.SH SYNOPSIS
.nf
#include <libssh2.h>

int
libssh2_userauth_keyboard_interactive_ex(LIBSSH2_SESSION *session,
                                         const char *username,
                                         unsigned int username_len,
            LIBSSH2_USERAUTH_KBDINT_RESPONSE_FUNC(*response_callback));
.SH DESCRIPTION
\fIsession\fP - Session instance as returned by
\fIlibssh2_session_init_ex(3)\fP.

\fIusername\fP - Name of user to attempt keyboard-interactive authentication
for.

\fIusername_len\fP - Length of username parameter.

\fIresponse_callback\fP - As authentication proceeds, the host issues several
(1 or more) challenges and requires responses. This callback will be called at
this moment. The callback is responsible to obtain responses for the
challenges, fill the provided data structure and then return
control. Responses will be sent to the host. String values will be free(3)ed
by the library. The callback prototype must match this:

.nf
 void response(const char *name,
               int name_len, const char *instruction,
               int instruction_len,
               int num_prompts,
               const LIBSSH2_USERAUTH_KBDINT_PROMPT *prompts,
               LIBSSH2_USERAUTH_KBDINT_RESPONSE *responses,
               void **abstract);
.fi

Attempts keyboard-interactive (challenge/response) authentication.

Note that many SSH servers will always issue a single "password" challenge,
requesting actual password as response, but it is not required by the
protocol, and various authentication schemes, such as smartcard authentication
may use keyboard-interactive authentication type too.
.SH RETURN VALUE
Return 0 on success or negative on failure.  It returns LIBSSH2_ERROR_EAGAIN
when it would otherwise block. While LIBSSH2_ERROR_EAGAIN is a negative
number, it isn't really a failure per se.
.SH ERRORS
\fILIBSSH2_ERROR_ALLOC\fP -  An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fLIBSSH2_ERROR_AUTHENTICATION_FAILED\fP - failed, invalid username/password
or public/private key.
.SH SEE ALSO
.BR libssh2_session_init_ex(3)
