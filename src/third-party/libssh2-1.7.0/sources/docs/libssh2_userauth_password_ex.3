.TH libssh2_userauth_password_ex 3 "1 Jun 2007" "libssh2 0.15" "libssh2 manual"
.SH NAME
libssh2_userauth_password_ex - authenticate a session with username and password
.SH SYNOPSIS
#include <libssh2.h>
.nf
int libssh2_userauth_password_ex(LIBSSH2_SESSION *session,
                    const char *username,
                    unsigned int username_len,
                    const char *password,
                    unsigned int password_len,
                    LIBSSH2_PASSWD_CHANGEREQ_FUNC((*passwd_change_cb)));

#define libssh2_userauth_password(session, username, password) \\
 libssh2_userauth_password_ex((session), (username), \\
                              strlen(username), \\
                              (password), strlen(password), NULL)
.SH DESCRIPTION
\fIsession\fP - Session instance as returned by 
.BR libssh2_session_init_ex(3)

\fIusername\fP - Name of user to attempt plain password authentication for.

\fIusername_len\fP - Length of username parameter.

\fIpassword\fP - Password to use for authenticating username.

\fIpassword_len\fP - Length of password parameter.

\fIpasswd_change_cb\fP - If the host accepts authentication but 
requests that the password be changed, this callback will be issued. 
If no callback is defined, but server required password change, 
authentication will fail.

Attempt basic password authentication. Note that many SSH servers 
which appear to support ordinary password authentication actually have 
it disabled and use Keyboard Interactive authentication (routed via 
PAM or another authentication backed) instead.

.SH RETURN VALUE
Return 0 on success or negative on failure.  It returns
LIBSSH2_ERROR_EAGAIN when it would otherwise block. While
LIBSSH2_ERROR_EAGAIN is a negative number, it isn't really a failure per se.

.SH ERRORS
Some of the errors this function may return include:

\fILIBSSH2_ERROR_ALLOC\fP -  An internal memory allocation call failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_PASSWORD_EXPIRED\fP - 

\fLIBSSH2_ERROR_AUTHENTICATION_FAILED\fP - failed, invalid username/password
or public/private key.
.SH SEE ALSO
.BR libssh2_session_init_ex(3)
