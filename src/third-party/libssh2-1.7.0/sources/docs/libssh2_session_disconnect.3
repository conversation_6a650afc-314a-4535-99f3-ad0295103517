.TH libssh2_session_disconnect 3 "20 Feb 2010" "libssh2 1.2.4" "libssh2 manual"
.SH NAME
libssh2_session_disconnect - convenience macro for \fIlibssh2_session_disconnect_ex(3)\fP calls
.SH SYNOPSIS
#include <libssh2.h>

int libssh2_session_disconnect(LIBSSH2_SESSION *session, const char *description);

.SH DESCRIPTION
This is a macro defined in a public libssh2 header file that is using the
underlying function \fIlibssh2_session_disconnect_ex(3)\fP.
.SH RETURN VALUE
See \fIlibssh2_session_disconnect_ex(3)\fP
.SH ERRORS
See \fIlibssh2_session_disconnect_ex(3)\fP
.SH SEE ALSO
.BR libssh2_session_disconnect_ex(3)
