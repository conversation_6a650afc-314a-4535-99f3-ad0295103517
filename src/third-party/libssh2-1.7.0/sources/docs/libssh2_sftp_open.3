.TH libssh2_sftp_open 3 "20 Feb 2010" "libssh2 1.2.4" "libssh2 manual"
.SH NAME
libssh2_sftp_open - convenience macro for \fIlibssh2_sftp_open_ex(3)\fP calls
.SH SYNOPSIS
#include <libssh2.h>

LIBSSH2_SFTP_HANDLE *
libssh2_sftp_open(LIBSSH2_SFTP *sftp, const char *path, unsigned long flags, long mode);

.SH DESCRIPTION
This is a macro defined in a public libssh2 header file that is using the
underlying function \fIlibssh2_sftp_open_ex(3)\fP.
.SH RETURN VALUE
See \fIlibssh2_sftp_open_ex(3)\fP
.SH ERRORS
See \fIlibssh2_sftp_open_ex(3)\fP
.SH SEE ALSO
.BR libssh2_sftp_open_ex(3)
