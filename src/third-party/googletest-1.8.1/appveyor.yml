version: '{build}'

os: Visual Studio 2015

environment:
  matrix:
    - compiler: msvc-15-seh
      generator: "Visual Studio 15 2017"
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017

    - compiler: msvc-15-seh
      generator: "Visual Studio 15 2017 Win64"
      APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
      enabled_on_pr: yes

    - compiler: msvc-14-seh
      generator: "Visual Studio 14 2015"
      enabled_on_pr: yes

    - compiler: msvc-14-seh
      generator: "Visual Studio 14 2015 Win64"

    - compiler: gcc-5.3.0-posix
      generator: "MinGW Makefiles"
      cxx_path: 'C:\mingw-w64\i686-5.3.0-posix-dwarf-rt_v4-rev0\mingw32\bin'

    - compiler: gcc-6.3.0-posix
      generator: "MinGW Makefiles"
      cxx_path: 'C:\mingw-w64\i686-6.3.0-posix-dwarf-rt_v5-rev1\mingw32\bin'

configuration:
  - Debug

build:
  verbosity: minimal

install:
- ps: |
    Write-Output "Compiler: $env:compiler"
    Write-Output "Generator: $env:generator"
    if (-not (Test-Path env:APPVEYOR_PULL_REQUEST_NUMBER)) {
      Write-Output "This is *NOT* a pull request build"
    } else {
      Write-Output "This is a pull request build"
      if (-not (Test-Path env:enabled_on_pr) -or $env:enabled_on_pr -ne "yes") {
        Write-Output "PR builds are *NOT* explicitly enabled"
      }
    }

    # git bash conflicts with MinGW makefiles
    if ($env:generator -eq "MinGW Makefiles") {
        $env:path = $env:path.replace("C:\Program Files\Git\usr\bin;", "")
        if ($env:cxx_path -ne "") {
            $env:path += ";$env:cxx_path"
        }
    }

build_script:
- ps: |
    # Only enable some builds for pull requests, the AppVeyor queue is too long.
    if ((Test-Path env:APPVEYOR_PULL_REQUEST_NUMBER) -And (-not (Test-Path env:enabled_on_pr) -or $env:enabled_on_pr -ne "yes")) {
      return
    }
    md _build -Force | Out-Null
    cd _build

    $conf = if ($env:generator -eq "MinGW Makefiles") {"-DCMAKE_BUILD_TYPE=$env:configuration"} else {"-DCMAKE_CONFIGURATION_TYPES=Debug;Release"}
    # Disable test for MinGW (gtest tests fail, gmock tests can not build)
    $gtest_build_tests = if ($env:generator -eq "MinGW Makefiles") {"-Dgtest_build_tests=OFF"} else {"-Dgtest_build_tests=ON"}
    $gmock_build_tests = if ($env:generator -eq "MinGW Makefiles") {"-Dgmock_build_tests=OFF"} else {"-Dgmock_build_tests=ON"}
    & cmake -G "$env:generator" $conf -Dgtest_build_samples=ON $gtest_build_tests $gmock_build_tests ..
    if ($LastExitCode -ne 0) {
        throw "Exec: $ErrorMessage"
    }
    $cmake_parallel = if ($env:generator -eq "MinGW Makefiles") {"-j2"} else  {"/m"}
    & cmake --build . --config $env:configuration -- $cmake_parallel
    if ($LastExitCode -ne 0) {
        throw "Exec: $ErrorMessage"
    }


skip_commits:
  files:
    - '**/*.md'

test_script:
- ps: |
    # Only enable some builds for pull requests, the AppVeyor queue is too long.
    if ((Test-Path env:APPVEYOR_PULL_REQUEST_NUMBER) -And (-not (Test-Path env:enabled_on_pr) -or $env:enabled_on_pr -ne "yes")) {
      return
    }
    if ($env:generator -eq "MinGW Makefiles") {
        return # No test available for MinGW
    }
    & ctest -C $env:configuration --timeout 600 --output-on-failure
    if ($LastExitCode -ne 0) {
        throw "Exec: $ErrorMessage"
    }

artifacts:
  - path: '_build/CMakeFiles/*.log'
    name: logs
  - path: '_build/Testing/**/*.xml'
    name: test_results
