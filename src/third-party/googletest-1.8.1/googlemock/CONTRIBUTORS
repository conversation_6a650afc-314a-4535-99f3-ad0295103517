# This file contains a list of people who've made non-trivial
# contribution to the Google C++ Mocking Framework project.  People
# who commit code to the project are encouraged to add their names
# here.  Please keep the list sorted by first names.

<PERSON><PERSON> <<EMAIL>>
<PERSON><PERSON><PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <da<PERSON>@google.com>
Dean <PERSON>evant
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON><PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON><PERSON><PERSON> <<EMAIL>>
Lev <PERSON><PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
<PERSON> <<PERSON>@gmail.com>
<PERSON><PERSON><PERSON> <<EMAIL>>
<PERSON> <<EMAIL>>
Paneendra Ba <<EMAIL>>
Paul Menage <<EMAIL>>
Piotr Kaminski <<EMAIL>>
Russ Rufer <<EMAIL>>
Sverre Sundsdal <<EMAIL>>
Takeshi Yoshino <<EMAIL>>
Vadim Berman <<EMAIL>>
Vlad Losev <<EMAIL>>
Wolfgang Klier <<EMAIL>>
Zhanyong Wan <<EMAIL>>
