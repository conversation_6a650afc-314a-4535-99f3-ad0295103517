This page lists all documentation markdown files for Google Mock **(the
current git version)**
-- **if you use a former version of Google Mock, please read the
documentation for that specific version instead (e.g. by checking out
the respective git branch/tag).**

  * [ForDummies](ForDummies.md) -- start here if you are new to Google Mock.
  * [CheatSheet](CheatSheet.md) -- a quick reference.
  * [CookBook](CookBook.md) -- recipes for doing various tasks using Google Mock.
  * [FrequentlyAskedQuestions](FrequentlyAskedQuestions.md) -- check here before asking a question on the mailing list.

To contribute code to Google Mock, read:

  * [CONTRIBUTING](../CONTRIBUTING.md) -- read this _before_ writing your first patch.
  * [Pump Manual](../../googletest/docs/PumpManual.md) -- how we generate some of Google Mock's source files.
