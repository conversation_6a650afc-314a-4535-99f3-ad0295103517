# Automake file

# Nonstandard package files for distribution.
EXTRA_DIST = LICENSE

# We may need to build our internally packaged gtest. If so, it will be
# included in the 'subdirs' variable.
SUBDIRS = $(subdirs)

# This is generated by the configure script, so clean it for distribution.
DISTCLEANFILES = scripts/gmock-config

# We define the global AM_CPPFLAGS as everything we compile includes from these
# directories.
AM_CPPFLAGS = $(GTEST_CPPFLAGS) -I$(srcdir)/include

# Modifies compiler and linker flags for pthreads compatibility.
if HAVE_PTHREADS
  AM_CXXFLAGS = @PTHREAD_CFLAGS@ -DGTEST_HAS_PTHREAD=1
  AM_LIBS = @PTHREAD_LIBS@
endif

# Build rules for libraries.
lib_LTLIBRARIES = lib/libgmock.la lib/libgmock_main.la

lib_libgmock_la_SOURCES = src/gmock-all.cc

pkginclude_HEADERS = \
  include/gmock/gmock-actions.h \
  include/gmock/gmock-cardinalities.h \
  include/gmock/gmock-generated-actions.h \
  include/gmock/gmock-generated-function-mockers.h \
  include/gmock/gmock-generated-matchers.h \
  include/gmock/gmock-generated-nice-strict.h \
  include/gmock/gmock-matchers.h \
  include/gmock/gmock-more-actions.h \
  include/gmock/gmock-more-matchers.h \
  include/gmock/gmock-spec-builders.h \
  include/gmock/gmock.h

pkginclude_internaldir = $(pkgincludedir)/internal
pkginclude_internal_HEADERS = \
  include/gmock/internal/gmock-generated-internal-utils.h \
  include/gmock/internal/gmock-internal-utils.h \
  include/gmock/internal/gmock-port.h \
  include/gmock/internal/custom/gmock-generated-actions.h \
  include/gmock/internal/custom/gmock-matchers.h \
  include/gmock/internal/custom/gmock-port.h

lib_libgmock_main_la_SOURCES = src/gmock_main.cc
lib_libgmock_main_la_LIBADD = lib/libgmock.la

# Build rules for tests. Automake's naming for some of these variables isn't
# terribly obvious, so this is a brief reference:
#
# TESTS -- Programs run automatically by "make check"
# check_PROGRAMS -- Programs built by "make check" but not necessarily run

TESTS=
check_PROGRAMS=
AM_LDFLAGS = $(GTEST_LDFLAGS)

# This exercises all major components of Google Mock.  It also
# verifies that libgmock works.
TESTS += test/gmock-spec-builders_test
check_PROGRAMS += test/gmock-spec-builders_test
test_gmock_spec_builders_test_SOURCES = test/gmock-spec-builders_test.cc
test_gmock_spec_builders_test_LDADD = $(GTEST_LIBS) lib/libgmock.la

# This tests using Google Mock in multiple translation units.  It also
# verifies that libgmock_main and libgmock work.
TESTS += test/gmock_link_test
check_PROGRAMS += test/gmock_link_test
test_gmock_link_test_SOURCES = \
  test/gmock_link2_test.cc \
  test/gmock_link_test.cc \
  test/gmock_link_test.h
test_gmock_link_test_LDADD = $(GTEST_LIBS) lib/libgmock_main.la  lib/libgmock.la

if HAVE_PYTHON
  # Tests that fused gmock files compile and work.
  TESTS += test/gmock_fused_test
  check_PROGRAMS += test/gmock_fused_test
  test_gmock_fused_test_SOURCES = \
    fused-src/gmock-gtest-all.cc \
    fused-src/gmock/gmock.h \
    fused-src/gmock_main.cc \
    fused-src/gtest/gtest.h \
    test/gmock_test.cc
  test_gmock_fused_test_CPPFLAGS = -I"$(srcdir)/fused-src"
endif

# Google Mock source files that we don't compile directly.
GMOCK_SOURCE_INGLUDES = \
  src/gmock-cardinalities.cc \
  src/gmock-internal-utils.cc \
  src/gmock-matchers.cc \
  src/gmock-spec-builders.cc \
  src/gmock.cc

EXTRA_DIST += $(GMOCK_SOURCE_INGLUDES)

# C++ tests that we don't compile using autotools.
EXTRA_DIST += \
  test/gmock-actions_test.cc \
  test/gmock_all_test.cc \
  test/gmock-cardinalities_test.cc \
  test/gmock_ex_test.cc \
  test/gmock-generated-actions_test.cc \
  test/gmock-generated-function-mockers_test.cc \
  test/gmock-generated-internal-utils_test.cc \
  test/gmock-generated-matchers_test.cc \
  test/gmock-internal-utils_test.cc \
  test/gmock-matchers_test.cc \
  test/gmock-more-actions_test.cc \
  test/gmock-nice-strict_test.cc \
  test/gmock-port_test.cc \
  test/gmock_stress_test.cc

# Python tests, which we don't run using autotools.
EXTRA_DIST += \
  test/gmock_leak_test.py \
  test/gmock_leak_test_.cc \
  test/gmock_output_test.py \
  test/gmock_output_test_.cc \
  test/gmock_output_test_golden.txt \
  test/gmock_test_utils.py

# Nonstandard package files for distribution.
EXTRA_DIST += \
  CHANGES \
  CONTRIBUTORS \
  make/Makefile

# Pump scripts for generating Google Mock headers.
# TODO(<EMAIL>): automate the generation of *.h from *.h.pump.
EXTRA_DIST += \
  include/gmock/gmock-generated-actions.h.pump \
  include/gmock/gmock-generated-function-mockers.h.pump \
  include/gmock/gmock-generated-matchers.h.pump \
  include/gmock/gmock-generated-nice-strict.h.pump \
  include/gmock/internal/gmock-generated-internal-utils.h.pump \
  include/gmock/internal/custom/gmock-generated-actions.h.pump

# Script for fusing Google Mock and Google Test source files.
EXTRA_DIST += scripts/fuse_gmock_files.py

# The Google Mock Generator tool from the cppclean project.
EXTRA_DIST += \
  scripts/generator/LICENSE \
  scripts/generator/README \
  scripts/generator/README.cppclean \
  scripts/generator/cpp/__init__.py \
  scripts/generator/cpp/ast.py \
  scripts/generator/cpp/gmock_class.py \
  scripts/generator/cpp/keywords.py \
  scripts/generator/cpp/tokenize.py \
  scripts/generator/cpp/utils.py \
  scripts/generator/gmock_gen.py

# Script for diagnosing compiler errors in programs that use Google
# Mock.
EXTRA_DIST += scripts/gmock_doctor.py

# CMake scripts.
EXTRA_DIST += \
  CMakeLists.txt

# Microsoft Visual Studio 2005 projects.
EXTRA_DIST += \
  msvc/2005/gmock.sln \
  msvc/2005/gmock.vcproj \
  msvc/2005/gmock_config.vsprops \
  msvc/2005/gmock_main.vcproj \
  msvc/2005/gmock_test.vcproj

# Microsoft Visual Studio 2010 projects.
EXTRA_DIST += \
  msvc/2010/gmock.sln \
  msvc/2010/gmock.vcxproj \
  msvc/2010/gmock_config.props \
  msvc/2010/gmock_main.vcxproj \
  msvc/2010/gmock_test.vcxproj

if HAVE_PYTHON
# gmock_test.cc does not really depend on files generated by the
# fused-gmock-internal rule.  However, gmock_test.o does, and it is
# important to include test/gmock_test.cc as part of this rule in order to
# prevent compiling gmock_test.o until all dependent files have been
# generated.
$(test_gmock_fused_test_SOURCES): fused-gmock-internal

# TODO(<EMAIL>): Find a way to add Google Tests's sources here.
fused-gmock-internal: $(pkginclude_HEADERS) $(pkginclude_internal_HEADERS) \
                      $(lib_libgmock_la_SOURCES) $(GMOCK_SOURCE_INGLUDES) \
                      $(lib_libgmock_main_la_SOURCES) \
                      scripts/fuse_gmock_files.py
	mkdir -p "$(srcdir)/fused-src"
	chmod -R u+w "$(srcdir)/fused-src"
	rm -f "$(srcdir)/fused-src/gtest/gtest.h"
	rm -f "$(srcdir)/fused-src/gmock/gmock.h"
	rm -f "$(srcdir)/fused-src/gmock-gtest-all.cc"
	"$(srcdir)/scripts/fuse_gmock_files.py" "$(srcdir)/fused-src"
	cp -f "$(srcdir)/src/gmock_main.cc" "$(srcdir)/fused-src"

maintainer-clean-local:
	rm -rf "$(srcdir)/fused-src"
endif

# Death tests may produce core dumps in the build directory. In case
# this happens, clean them to keep distcleancheck happy.
CLEANFILES = core

# Disables 'make install' as installing a compiled version of Google
# Mock can lead to undefined behavior due to violation of the
# One-Definition Rule.

install-exec-local:
	echo "'make install' is dangerous and not supported. Instead, see README for how to integrate Google Mock into your build system."
	false

install-data-local:
	echo "'make install' is dangerous and not supported. Instead, see README for how to integrate Google Mock into your build system."
	false
