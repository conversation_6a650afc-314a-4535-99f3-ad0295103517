# Build matrix / environment variable are explained on:
# https://docs.travis-ci.com/user/customizing-the-build/
# This file can be validated on:
# http://lint.travis-ci.org/

sudo: false
language: cpp

# Define the matrix explicitly, manually expanding the combinations of (os, compiler, env).
# It is more tedious, but grants us far more flexibility.
matrix:
  include:
    - os: linux
      compiler: gcc
      sudo : true
      install: ./ci/install-linux.sh && ./ci/log-config.sh
      script: ./ci/build-linux-bazel.sh
    - os: linux
      compiler: clang
      sudo : true
      install: ./ci/install-linux.sh && ./ci/log-config.sh
      script: ./ci/build-linux-bazel.sh
    - os: linux
      group: deprecated-2017Q4
      compiler: gcc
      install: ./ci/install-linux.sh && ./ci/log-config.sh
      script: ./ci/build-linux-autotools.sh
    - os: linux
      group: deprecated-2017Q4
      compiler: gcc
      env: BUILD_TYPE=Debug VERBOSE=1 CXX_FLAGS=-std=c++11
    - os: linux
      group: deprecated-2017Q4
      compiler: clang
      env: BUILD_TYPE=Debug VERBOSE=1
    - os: linux
      group: deprecated-2017Q4
      compiler: clang
      env: BUILD_TYPE=Release VERBOSE=1 CXX_FLAGS=-std=c++11
    - os: linux
      compiler: clang
      env: BUILD_TYPE=Release VERBOSE=1 CXX_FLAGS=-std=c++11 NO_EXCEPTION=ON NO_RTTI=ON COMPILER_IS_GNUCXX=ON
    - os: osx
      compiler: gcc
      env: BUILD_TYPE=Debug VERBOSE=1
    - os: osx
      compiler: gcc
      env: BUILD_TYPE=Release VERBOSE=1 CXX_FLAGS=-std=c++11
    - os: osx
      compiler: clang
      env: BUILD_TYPE=Debug VERBOSE=1
      if: type != pull_request
    - os: osx
      env: BUILD_TYPE=Release VERBOSE=1 CXX_FLAGS=-std=c++11
      if: type != pull_request

# These are the install and build (script) phases for the most common entries in the matrix.  They could be included
# in each entry in the matrix, but that is just repetitive.
install:
  - ./ci/install-${TRAVIS_OS_NAME}.sh
  - . ./ci/env-${TRAVIS_OS_NAME}.sh
  - ./ci/log-config.sh

script: ./ci/travis.sh

# For sudo=false builds this section installs the necessary dependencies.
addons:
  apt:
    # List of whitelisted in travis packages for ubuntu-precise can be found here:
    #   https://github.com/travis-ci/apt-package-whitelist/blob/master/ubuntu-precise
    # List of whitelisted in travis apt-sources:
    #   https://github.com/travis-ci/apt-source-whitelist/blob/master/ubuntu.json
    sources:
    - ubuntu-toolchain-r-test
    - llvm-toolchain-precise-3.9
    packages:
    - g++-4.9
    - clang-3.9

notifications:
  email: false
