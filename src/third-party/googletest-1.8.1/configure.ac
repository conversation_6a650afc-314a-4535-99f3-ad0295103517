AC_INIT([Google C++ Mocking and Testing Frameworks],
        [1.8.0],
        [<EMAIL>],
        [googletest])

# Provide various options to initialize the Autoconf and configure processes.
AC_PREREQ([2.59])
AC_CONFIG_SRCDIR([./README.md])
AC_CONFIG_AUX_DIR([build-aux])
AC_CONFIG_FILES([Makefile])
AC_CONFIG_SUBDIRS([googletest googlemock])

AM_INIT_AUTOMAKE

# Output the generated files. No further autoconf macros may be used.
AC_OUTPUT
