.TH libssh2_poll_channel_read 3 "14 Dec 2006" "libssh2 0.15" "libssh2 manual"
.SH NAME
libssh2_poll_channel_read - check if data is available
.SH SYNOPSIS
#include <libssh2.h>

int libssh2_poll_channel_read(LIBSSH2_CHANNEL *channel, int extended);
.SH DESCRIPTION
This function is deprecated. Do note use.

\fIlibssh2_poll_channel_read(3)\fP checks to see if data is available in the
\fIchannel\fP's read buffer. No attempt is made with this method to see if
packets are available to be processed. For full polling support, use
\fIlibssh2_poll(3)\fP.
.SH RETURN VALUE
Returns 1 when data is available and 0 otherwise.
.SH SEE ALSO
.BR libssh2_poll(3)
