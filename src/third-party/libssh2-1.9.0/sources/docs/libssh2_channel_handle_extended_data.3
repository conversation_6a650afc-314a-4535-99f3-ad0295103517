.TH libssh2_channel_handle_extended_data 3 "1 Jun 2007" "libssh2 0.15" "libssh2 manual"
.SH NAME
libssh2_channel_handle_extended_data - set extended data handling mode
.SH SYNOPSIS
#include <libssh2.h>

void 
libssh2_channel_handle_extended_data(LIBSSH2_CHANNEL *channel, int ignore_mode);

.SH DESCRIPTION
This function is deprecated. Use the
\fIlibssh2_channel_handle_extended_data2(3)\fP function instead!

\fIchannel\fP - Active channel stream to change extended data handling on.

\fIignore_mode\fP - One of the three LIBSSH2_CHANNEL_EXTENDED_DATA_* Constants.
.br
\fBLIBSSH2_CHANNEL_EXTENDED_DATA_NORMAL\fP: Queue extended data for eventual 
reading
.br
\fBLIBSSH2_CHANNEL_EXTENDED_DATA_MERGE\fP: Treat extended data and ordinary
data the same. Merge all substreams such that calls to
\fIlibssh2_channel_read(3)\fP will pull from all substreams on a
first-in/first-out basis.
.br
\fBLIBSSH2_CHANNEL_EXTENDED_DATA_IGNORE\fP: Discard all extended data as it 
arrives.

Change how a channel deals with extended data packets. By default all extended
data is queued until read by \fIlibssh2_channel_read_ex(3)\fP
.SH RETURN VALUE
None.
.SH SEE ALSO
.BR libssh2_channel_handle_extended_data2(3)
.BR libssh2_channel_read_ex(3)
