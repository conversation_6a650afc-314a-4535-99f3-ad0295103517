.TH libssh2_session_init 3 "20 Feb 2010" "libssh2 1.2.4" "libssh2 manual"
.SH NAME
libssh2_session_init - convenience macro for \fIlibssh2_session_init_ex(3)\fP calls
.SH SYNOPSIS
#include <libssh2.h>

LIBSSH2_SESSION *
libssh2_session_init(void);

.SH DESCRIPTION
This is a macro defined in a public libssh2 header file that is using the
underlying function \fIlibssh2_session_init_ex(3)\fP.
.SH RETURN VALUE
See \fIlibssh2_session_init_ex(3)\fP
.SH ERRORS
See \fIlibssh2_session_init_ex(3)\fP
.SH SEE ALSO
.BR libssh2_session_init_ex(3)
