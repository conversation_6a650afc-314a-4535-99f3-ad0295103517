.\"
.\" Copyright (c) 2009 by <PERSON>
.\"
.TH libssh2_knownhost_readline 3 "28 May 2009" "libssh2 1.2" "libssh2 manual"
.SH NAME
libssh2_knownhost_readline - read a known host line
.SH SYNOPSIS
#include <libssh2.h>

int libssh2_knownhost_readline(LIBSSH2_KNOWNHOSTS *hosts,
                               const char *line, size_t len, int type):
.SH DESCRIPTION
Tell libssh2 to read a buffer as it if is a line from a known hosts file.

\fIline\fP points to the start of the line

\fIlen\fP is the length of the line in bytes

\fItype\fP specifies what file type it is, and
\fILIBSSH2_KNOWNHOST_FILE_OPENSSH\fP is the only currently supported
format. This file is normally found named ~/.ssh/known_hosts
.SH RETURN VALUE
Returns a regular libssh2 error code, where negative values are error codes
and 0 indicates success.
.SH AVAILABILITY
Added in libssh2 1.2
.SH SEE ALSO
.BR libssh2_knownhost_get(3)
.BR libssh2_knownhost_writeline(3)
.BR libssh2_knownhost_readfile(3)
