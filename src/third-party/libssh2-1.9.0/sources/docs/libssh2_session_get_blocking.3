.TH libssh2_session_get_blocking 3 "1 Jun 2007" "libssh2 0.15" "libssh2 manual"
.SH NAME
libssh2_session_get_blocking - TODO
.SH SYNOPSIS
int libssh2_session_get_blocking(LIBSSH2_SESSION *session);
.SH DESCRIPTION
Returns 0 if the state of the session has previously be set to non-blocking
and it returns 1 if the state was set to blocking.
.SH RETURN VALUE
See description.
.SH SEE ALSO
.BR libssh2_session_set_blocking(3)
