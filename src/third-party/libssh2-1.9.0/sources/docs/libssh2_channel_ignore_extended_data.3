.TH libssh2_channel_ignore_extended_data 3 "20 Feb 2010" "libssh2 1.2.4" "libssh2 manual"
.SH NAME
libssh2_channel_ignore_extended_data - convenience macro for \fIlibssh2_channel_handle_extended_data(3)\fP calls
.SH SYNOPSIS
#include <libssh2.h>

libssh2_channel_ignore_extended_data(arguments)

.SH DESCRIPTION
This function is deprecated. Use the
\fIlibssh2_channel_handle_extended_data2(3)\fP function instead!

This is a macro defined in a public libssh2 header file that is using the
underlying function \fIlibssh2_channel_handle_extended_data(3)\fP.
.SH RETURN VALUE
See \fIlibssh2_channel_handle_extended_data(3)\fP
.SH ERRORS
See \fIlibssh2_channel_handle_extended_data(3)\fP
.SH SEE ALSO
.BR libssh2_channel_handle_extended_data(3)
