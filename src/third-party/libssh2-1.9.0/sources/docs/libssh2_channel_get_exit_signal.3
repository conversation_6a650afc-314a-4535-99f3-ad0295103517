.TH libssh2_channel_get_exit_signal 3 "4 Oct 2010" "libssh2 1.2.8" "libssh2 manual"
.SH NAME
libssh2_channel_get_exit_signal - get the remote exit signal
.SH SYNOPSIS
#include <libssh2.h>

int 
libssh2_channel_get_exit_signal(LIBSSH2_CHANNEL *channel, char **exitsignal, size_t *exitsignal_len, char **errmsg, size_t *errmsg_len, char **langtag, size_t *langtag_len);

.SH DESCRIPTION
\fIchannel\fP - Closed channel stream to retrieve exit signal from.

\fIexitsignal\fP - If not NULL, is populated by reference with the exit signal
(without leading "SIG"). Note that the string is stored in a newly allocated
buffer. If the remote program exited cleanly, the referenced string pointer
will be set to NULL. 

\fIexitsignal_len\fP - If not NULL, is populated by reference with the length
of exitsignal. 

\fIerrmsg\fP - If not NULL, is populated by reference with the error message
(if provided by remote server, if not it will be set to NULL). Note that the
string is stored in a newly allocated buffer.

\fIerrmsg_len\fP - If not NULL, is populated by reference with the length of errmsg.

\fIlangtag\fP - If not NULL, is populated by reference with the language tag 
(if provided by remote server, if not it will be set to NULL). Note that the
string is stored in a newly allocated buffer.

\fIlangtag_len\fP - If not NULL, is populated by reference with the length of langtag.

.SH RETURN VALUE
Numeric error code corresponding to the the Error Code constants.
