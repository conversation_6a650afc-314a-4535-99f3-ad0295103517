.TH libssh2_session_get_timeout 3 "4 May 2011" "libssh2 1.2.9" "libssh2 manual"
.SH NAME
libssh2_session_get_timeout - get the timeout for blocking functions
.SH SYNOPSIS
#include <libssh2.h>
.nf
long libssh2_session_get_timeout(LIBSSH2_SESSION *session);
.SH DESCRIPTION
Returns the \fBtimeout\fP (in milliseconds) for how long a blocking the
libssh2 function calls may wait until they consider the situation an error and
return LIBSSH2_ERROR_TIMEOUT.

By default libssh2 has no timeout (zero) for blocking functions.
.SH RETURN VALUE
The value of the timeout setting.
.SH AVAILABILITY
Added in 1.2.9
.SH SEE ALSO
.BR libssh2_session_set_timeout(3)
