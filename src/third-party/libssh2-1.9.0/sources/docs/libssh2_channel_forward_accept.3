.TH libssh2_channel_forward_accept 3 "1 Jun 2007" "libssh2 0.15" "libssh2 manual"
.SH NAME
libssh2_channel_forward_accept - accept a queued connection
.SH SYNOPSIS
#include <libssh2.h>

LIBSSH2_CHANNEL *
libssh2_channel_forward_accept(LIBSSH2_LISTENER *listener);

.SH DESCRIPTION
\fIlistener\fP is a forwarding listener instance as returned by
\fBlibssh2_channel_forward_listen_ex(3)\fP.
.SH RETURN VALUE
A newly allocated channel instance or NULL on failure.
.SH ERRORS
When this function returns NULL use \fIlibssh2_session_last_errno(3)\fP to
extract the error code. If that code is \fILIBSSH2_ERROR_EAGAIN\fP, the
session is set to do non-blocking I/O but the call would block.
.SH SEE ALSO
.BR libssh2_channel_forward_listen_ex(3)
