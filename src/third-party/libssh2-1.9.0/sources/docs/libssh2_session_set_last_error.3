.TH libssh2_session_set_last_error 3 "26 Oct 2015" "libssh2 1.6.1" "libssh2 manual"
.SH NAME
libssh2_session_set_last_error - sets the internal error state
.SH SYNOPSIS
#include <libssh2.h>

int
libssh2_session_set_last_error(LIBSSH2_SESSION *session, int errcode, const char *errmsg)

.SH DESCRIPTION
\fIsession\fP - Session instance as returned by
.BR libssh2_session_init_ex(3)

\fIerrcode\fP - One of the error codes as defined in the public
libssh2 header file.

\fIerrmsg\fP - If not NULL, a copy of the given string is stored
inside the session object as the error message.

This function is provided for high level language wrappers
(i.e. Python or Perl) and other libraries that may extend libssh2 with
additional features while still relying on its error reporting
mechanism.

.SH RETURN VALUE
Numeric error code corresponding to the the Error Code constants.

.SH AVAILABILITY
Added in 1.6.1

.SH SEE ALSO
.BR libssh2_session_last_error(3)
.BR libssh2_session_last_errno(3)
