.TH libssh2_session_handshake 3 "7 Oct 2010" "libssh2 1.2.8" "libssh2 manual"
.SH NAME
libssh2_session_handshake - perform the SSH handshake
.SH SYNOPSIS
#include <libssh2.h>

int
libssh2_session_handshake(LIBSSH2_SESSION *session, libssh2_socket_t socket);
.SH DESCRIPTION
\fIsession\fP - Session instance as returned by
.BR libssh2_session_init_ex(3)

\fIsocket\fP - Connected socket descriptor. Typically a TCP connection
though the protocol allows for any reliable transport and the library will
attempt to use any berkeley socket.

Begin transport layer protocol negotiation with the connected host.
.SH RETURN VALUE
Returns 0 on success, negative on failure.
.SH ERRORS
\fILIBSSH2_ERROR_SOCKET_NONE\fP - The socket is invalid.

\fILIBSSH2_ERROR_BANNER_SEND\fP - Unable to send banner to remote host.

\fILIBSSH2_ERROR_KEX_FAILURE\fP - >Encryption key exchange with the remote
host failed.

\fILIBSSH2_ERROR_SOCKET_SEND\fP - Unable to send data on socket.

\fILIBSSH2_ERROR_SOCKET_DISCONNECT\fP - The socket was disconnected.

\fILIBSSH2_ERROR_PROTO\fP - An invalid SSH protocol response was received on
the socket.

\fILIBSSH2_ERROR_EAGAIN\fP - Marked for non-blocking I/O but the call would block.
.SH AVAILABILITY
Added in 1.2.8
.SH SEE ALSO
.BR libssh2_session_free(3)
.BR libssh2_session_init_ex(3)
