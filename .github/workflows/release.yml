name: Release

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      tag:
        description: 'Release tag'
        required: true
        type: string

env:
  QT_VERSION: "5.12.8"
  OPENSSL_VERSION: "1.1.1f"

jobs:
  build-and-release:
    strategy:
      matrix:
        os: [macos-12, ubuntu-20.04]
        include:
          - os: macos-12
            artifact_name: robo3t-macos
            binary_path: "build/release/install/Robo 3T.app"
            package_path: "build/release/package/*.dmg"
          - os: ubuntu-20.04
            artifact_name: robo3t-linux
            binary_path: "build/release/install/bin/robo3t"
            package_path: "build/release/package/*.tar.gz"
    
    runs-on: ${{ matrix.os }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        submodules: recursive
        fetch-depth: 0

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.8'  # Use Python 3.8 for better compatibility

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        # Install compatible versions for older dependencies
        pip install "setuptools<60"  # Use older setuptools that has Feature class
        pip install scons==3.1.2
        # Pre-install compatible zope.interface version
        pip install "zope.interface>=5.0.0"

    - name: Install system dependencies (macOS)
      if: matrix.os == 'macos-12'
      run: |
        brew update
        brew install cmake ninja pkg-config

    - name: Install system dependencies (Linux)
      if: matrix.os == 'ubuntu-20.04'
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential cmake ninja-build pkg-config \
          libcurl4-openssl-dev libgl1-mesa-dev

    - name: Install Qt
      uses: jurplel/install-qt-action@v4
      with:
        version: ${{ env.QT_VERSION }}
        host: ${{ matrix.os == 'macos-12' && 'mac' || 'linux' }}
        target: 'desktop'
        arch: ${{ matrix.os == 'macos-12' && 'clang_64' || 'gcc_64' }}
        dir: '${{ runner.temp }}/qt'
        install-deps: 'true'
        cache: 'true'

    - name: Cache OpenSSL
      id: cache-openssl
      uses: actions/cache@v4
      with:
        path: /opt/openssl-${{ env.OPENSSL_VERSION }}
        key: ${{ runner.os }}-openssl-${{ env.OPENSSL_VERSION }}

    - name: Build OpenSSL (macOS)
      if: matrix.os == 'macos-12' && steps.cache-openssl.outputs.cache-hit != 'true'
      run: |
        cd /tmp
        curl -L -o openssl-${{ env.OPENSSL_VERSION }}.tar.gz \
          https://www.openssl.org/source/old/1.1.1/openssl-${{ env.OPENSSL_VERSION }}.tar.gz
        tar -xf openssl-${{ env.OPENSSL_VERSION }}.tar.gz
        cd openssl-${{ env.OPENSSL_VERSION }}
        sudo mkdir -p /opt/openssl-${{ env.OPENSSL_VERSION }}
        sudo chown -R $(whoami) /opt/openssl-${{ env.OPENSSL_VERSION }}
        ./Configure darwin64-x86_64-cc shared no-ssl2 no-ssl3 no-comp \
          -mmacosx-version-min=10.14 --prefix=/opt/openssl-${{ env.OPENSSL_VERSION }}
        make -j$(sysctl -n hw.ncpu)
        make install

    - name: Build OpenSSL (Linux)
      if: matrix.os == 'ubuntu-20.04' && steps.cache-openssl.outputs.cache-hit != 'true'
      run: |
        cd /tmp
        wget https://www.openssl.org/source/old/1.0.2/openssl-1.0.2o.tar.gz
        tar -xf openssl-1.0.2o.tar.gz
        cd openssl-1.0.2o
        sudo mkdir -p /opt/openssl-${{ env.OPENSSL_VERSION }}
        sudo chown -R $(whoami) /opt/openssl-${{ env.OPENSSL_VERSION }}
        ./config shared --prefix=/opt/openssl-${{ env.OPENSSL_VERSION }}
        make -j$(nproc)
        make install

    - name: Cache Robo Shell
      id: cache-roboshell
      uses: actions/cache@v4
      with:
        path: /opt/robo-shell
        key: ${{ runner.os }}-roboshell-v4.2

    - name: Build Robo Shell
      if: steps.cache-roboshell.outputs.cache-hit != 'true'
      run: |
        cd /tmp
        git clone --depth 1 --branch roboshell-v4.2 \
          https://github.com/paralect/robomongo-shell.git robo-shell
        cd robo-shell

        # Try to fix dependency compatibility issues
        python3 ${{ github.workspace }}/scripts/fix-roboshell-deps.py /tmp/robo-shell || true

        # Try to install pip requirements with fixed versions
        if ! pip3 install --user -r etc/pip/compile-requirements.txt; then
          echo "Original requirements failed, using fallback requirements..."
          pip3 install --user -r ${{ github.workspace }}/scripts/roboshell-requirements.txt
        fi

        if ! pip3 install --user -r etc/pip/dev-requirements.txt; then
          echo "Dev requirements failed, continuing with essential packages..."
          pip3 install --user typing cheetah3 pyyaml requests
        fi
        
        export ROBOMONGO_CMAKE_PREFIX_PATH="${{ runner.temp }}/qt/Qt/${{ env.QT_VERSION }}/${{ matrix.os == 'macos-12' && 'clang_64' || 'gcc_64' }};/opt/robo-shell;/opt/openssl-${{ env.OPENSSL_VERSION }}"
        
        bin/build
        
        sudo mkdir -p /opt/robo-shell
        sudo cp -r . /opt/robo-shell/

    - name: Set environment variables
      run: |
        echo "ROBOMONGO_CMAKE_PREFIX_PATH=${{ runner.temp }}/qt/Qt/${{ env.QT_VERSION }}/${{ matrix.os == 'macos-12' && 'clang_64' || 'gcc_64' }};/opt/robo-shell;/opt/openssl-${{ env.OPENSSL_VERSION }}" >> $GITHUB_ENV

    - name: Configure Robo 3T
      run: bin/configure release

    - name: Build Robo 3T
      run: bin/build release

    - name: Install Robo 3T
      run: bin/install release

    - name: Create Package
      run: bin/pack release

    - name: Upload Release Assets
      uses: softprops/action-gh-release@v1
      with:
        tag_name: ${{ github.event.inputs.tag || github.ref_name }}
        files: ${{ matrix.package_path }}
        name: Robo 3T ${{ github.event.inputs.tag || github.ref_name }}
        draft: false
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Upload Build Artifacts
      uses: actions/upload-artifact@v4
      with:
        name: ${{ matrix.artifact_name }}
        path: |
          ${{ matrix.binary_path }}
          ${{ matrix.package_path }}
        retention-days: 90
