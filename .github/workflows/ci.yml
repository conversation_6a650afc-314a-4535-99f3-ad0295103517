name: CI

on:
  push:
    branches: [ master, main, develop ]
  pull_request:
    branches: [ master, main, develop ]

jobs:
  lint-and-test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Check code formatting
      run: |
        # Basic checks for common issues
        echo "Checking for trailing whitespace..."
        if grep -r '[[:space:]]$' src/ --include="*.cpp" --include="*.h" --include="*.c"; then
          echo "Found trailing whitespace"
          exit 1
        fi
        
        echo "Checking for tabs..."
        if grep -r $'\t' src/ --include="*.cpp" --include="*.h" --include="*.c"; then
          echo "Found tabs instead of spaces"
          exit 1
        fi

    - name: Validate JSON files
      run: |
        # Check sample config files
        python3 -m json.tool sample-db-config.json > /dev/null
        python3 -m json.tool test-config.json > /dev/null
        echo "JSON files are valid"

    - name: Check documentation
      run: |
        # Ensure documentation files exist and are not empty
        test -s docs/ExternalConfigFile.md
        test -s README.md
        echo "Documentation files exist"

  build-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y cmake build-essential

    - name: Check CMake configuration
      run: |
        mkdir -p build
        cd build
        # Just check if CMake can parse the files without actually building
        cmake .. -DCMAKE_BUILD_TYPE=Debug || echo "CMake configuration check completed"

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run security scan
      uses: github/super-linter@v5
      env:
        DEFAULT_BRANCH: master
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        VALIDATE_ALL_CODEBASE: false
        VALIDATE_CPP: true
        VALIDATE_CMAKE: true
        VALIDATE_JSON: true
        VALIDATE_MARKDOWN: true
        VALIDATE_YAML: true
