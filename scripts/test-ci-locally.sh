#!/bin/bash

# <PERSON><PERSON><PERSON> to test CI checks locally before pushing to GitHub
# This mimics some of the checks performed in the CI workflow

set -e

echo "🔍 Running local CI checks for Robo 3T..."
echo "=========================================="

# Check if we're in the right directory
if [ ! -f "CMakeLists.txt" ]; then
    echo "❌ Error: Please run this script from the root of the Robo 3T repository"
    exit 1
fi

echo "📁 Current directory: $(pwd)"
echo ""

# 1. Check for trailing whitespace
echo "🔍 Checking for trailing whitespace..."
if find src/ -name "*.cpp" -o -name "*.h" -o -name "*.c" | xargs grep -l '[[:space:]]$' 2>/dev/null; then
    echo "❌ Found files with trailing whitespace (see above)"
    echo "   Fix with: find src/ -name '*.cpp' -o -name '*.h' -o -name '*.c' | xargs sed -i 's/[[:space:]]*$//'"
    exit 1
else
    echo "✅ No trailing whitespace found"
fi

# 2. Check for tabs
echo ""
echo "🔍 Checking for tabs instead of spaces..."
if find src/ -name "*.cpp" -o -name "*.h" -o -name "*.c" | xargs grep -l $'\t' 2>/dev/null; then
    echo "❌ Found files with tabs instead of spaces (see above)"
    echo "   Fix with: find src/ -name '*.cpp' -o -name '*.h' -o -name '*.c' | xargs sed -i 's/\t/    /g'"
    exit 1
else
    echo "✅ No tabs found"
fi

# 3. Validate JSON files
echo ""
echo "🔍 Validating JSON files..."
json_files=("sample-db-config.json" "test-config.json")
for file in "${json_files[@]}"; do
    if [ -f "$file" ]; then
        if python3 -m json.tool "$file" > /dev/null 2>&1; then
            echo "✅ $file is valid JSON"
        else
            echo "❌ $file is invalid JSON"
            exit 1
        fi
    else
        echo "⚠️  $file not found (skipping)"
    fi
done

# 4. Check documentation files
echo ""
echo "🔍 Checking documentation files..."
doc_files=("docs/ExternalConfigFile.md" "README.md" ".github/README.md")
for file in "${doc_files[@]}"; do
    if [ -f "$file" ] && [ -s "$file" ]; then
        echo "✅ $file exists and is not empty"
    else
        echo "❌ $file is missing or empty"
        exit 1
    fi
done

# 5. Check CMake configuration (if cmake is available)
echo ""
echo "🔍 Checking CMake configuration..."
if command -v cmake >/dev/null 2>&1; then
    mkdir -p build/test-ci
    cd build/test-ci
    if cmake ../.. -DCMAKE_BUILD_TYPE=Debug >/dev/null 2>&1; then
        echo "✅ CMake configuration is valid"
    else
        echo "⚠️  CMake configuration has issues (this may be due to missing dependencies)"
    fi
    cd ../..
    rm -rf build/test-ci
else
    echo "⚠️  CMake not found, skipping configuration check"
fi

# 6. Check for common C++ issues
echo ""
echo "🔍 Checking for common C++ issues..."

# Check for missing includes
echo "   Checking for potential missing includes..."
missing_includes=0
if grep -r "std::" src/ --include="*.cpp" --include="*.h" | grep -v "#include" | head -5; then
    echo "   ⚠️  Found std:: usage - ensure proper includes are present"
fi

# Check for potential memory leaks (basic check)
echo "   Checking for potential memory management issues..."
if grep -r "new " src/ --include="*.cpp" | grep -v "delete\|smart_ptr\|unique_ptr\|shared_ptr" | head -3; then
    echo "   ⚠️  Found 'new' without obvious cleanup - check for memory leaks"
fi

# 7. Check workflow files
echo ""
echo "🔍 Checking GitHub Actions workflow files..."
workflow_files=(".github/workflows/ci.yml" ".github/workflows/build-macos.yml" ".github/workflows/release.yml")
for file in "${workflow_files[@]}"; do
    if [ -f "$file" ]; then
        # Basic YAML syntax check
        if python3 -c "import yaml; yaml.safe_load(open('$file'))" 2>/dev/null; then
            echo "✅ $file is valid YAML"
        else
            echo "❌ $file has YAML syntax errors"
            exit 1
        fi
    else
        echo "❌ $file is missing"
        exit 1
    fi
done

# 8. Check for executable permissions on scripts
echo ""
echo "🔍 Checking script permissions..."
script_files=("bin/build" "bin/configure" "bin/clean" "bin/install" "bin/pack" "bin/run")
for file in "${script_files[@]}"; do
    if [ -f "$file" ]; then
        if [ -x "$file" ]; then
            echo "✅ $file is executable"
        else
            echo "⚠️  $file is not executable (run: chmod +x $file)"
        fi
    fi
done

echo ""
echo "🎉 Local CI checks completed!"
echo ""
echo "📝 Summary:"
echo "   - Code formatting: ✅"
echo "   - JSON validation: ✅"
echo "   - Documentation: ✅"
echo "   - CMake config: ✅ (or skipped)"
echo "   - Workflow files: ✅"
echo ""
echo "🚀 Your code is ready for GitHub Actions!"
echo ""
echo "💡 To run the actual GitHub Actions locally, consider using:"
echo "   - act (https://github.com/nektos/act)"
echo "   - GitHub CLI with 'gh workflow run'"
