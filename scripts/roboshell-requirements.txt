# Compatible requirements for Robo Shell build
# This file replaces the problematic requirements from the original Robo Shell

# Core build tools
setuptools<60
wheel>=0.37.0
pip>=21.0

# Python typing support
typing>=3.7.4

# Template engine
Cheetah3>=3.2.0

# YAML processing
PyYAML>=5.4.0

# HTTP requests
requests>=2.25.0

# Interface definitions (compatible version)
zope.interface>=5.0.0

# Build system
scons>=3.1.2

# Additional utilities that might be needed
six>=1.15.0
setuptools-scm>=6.0.0
