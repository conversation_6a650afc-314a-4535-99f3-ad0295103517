#!/usr/bin/env python3
"""
<PERSON>ript to fix Robo Shell dependency issues in CI/CD environments.
This script patches the requirements files to use compatible versions.
"""

import os
import sys
import re

def fix_requirements_file(filepath):
    """Fix a requirements file by updating problematic dependencies."""
    if not os.path.exists(filepath):
        print(f"Warning: {filepath} not found, skipping...")
        return
    
    print(f"Fixing {filepath}...")
    
    with open(filepath, 'r') as f:
        content = f.read()
    
    # Replace problematic zope.interface version
    content = re.sub(r'zope\.interface==4\.6\.0', 'zope.interface>=5.0.0', content)
    
    # Replace other problematic packages
    replacements = {
        'setuptools==40.6.2': 'setuptools<60',
        'setuptools==40.8.0': 'setuptools<60',
        'wheel==0.32.3': 'wheel>=0.37.0',
        'wheel==0.33.1': 'wheel>=0.37.0',
    }
    
    for old, new in replacements.items():
        content = re.sub(re.escape(old), new, content)
    
    # Write back the fixed content
    with open(filepath, 'w') as f:
        f.write(content)
    
    print(f"Fixed {filepath}")

def main():
    """Main function to fix all requirements files."""
    if len(sys.argv) > 1:
        robo_shell_dir = sys.argv[1]
    else:
        robo_shell_dir = '/tmp/robo-shell'
    
    print(f"Fixing Robo Shell dependencies in {robo_shell_dir}")
    
    # List of requirements files to fix
    req_files = [
        'etc/pip/compile-requirements.txt',
        'etc/pip/dev-requirements.txt',
        'etc/pip/components/external_auth.req',
        'etc/pip/components/platform.req',
    ]
    
    for req_file in req_files:
        full_path = os.path.join(robo_shell_dir, req_file)
        fix_requirements_file(full_path)
    
    print("Dependency fixing completed!")

if __name__ == '__main__':
    main()
