#!/usr/bin/env bash

BIN_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"       # Get directory of this file
PROJECT_DIR=$(dirname $BIN_DIR)                                   # Get root directory of project
source $BIN_DIR/common/setup $1

echo "Note: ./install must be executed before this script."
if [ "$(uname -s)" == "Darwin" ]; then
    echo "Starting $BUILD_DIR/install/Robo 3T.app/Contents/MacOS/Robo 3T"
    "$BUILD_DIR/install/Robo 3T.app/Contents/MacOS/Robo 3T"
else
    echo "Starting $BUILD_DIR/install/bin/robo3t"
    "$BUILD_DIR/install/bin/robo3t"    
fi