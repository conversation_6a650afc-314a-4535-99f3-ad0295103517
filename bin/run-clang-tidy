#!/usr/bin/env bash

### Common Setup
BIN_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"       # Get directory of this file
PROJECT_DIR=$(dirname $BIN_DIR)                                   # Get root directory of project
source $BIN_DIR/common/setup $1 # $1: release or debug

### Setup
CLANG_TIDY_BUILD_DIR=$PROJECT_DIR/build/clang-tidy-$BUILD_TYPE
rm -rf $CLANG_TIDY_BUILD_DIR
mkdir -p $CLANG_TIDY_BUILD_DIR && cd $CLANG_TIDY_BUILD_DIR

### Configure cmake
# Info: $BUILD_TYPE == Release or Debug
cmake -D "CMAKE_EXPORT_COMPILE_COMMANDS=ON" -D "CMAKE_PREFIX_PATH=$PREFIX_PATH" -D "CMAKE_BUILD_TYPE=$BUILD_TYPE" -D "CMAKE_INSTALL_PREFIX=$INSTALL_PREFIX" "${@:2}" $PROJECT_DIR
echo -e "\n------------------------------------------------"
echo "clang-tidy configured. Mode: $BUILD_TYPE"
echo -e "------------------------------------------------\n"

### Run clang-tidy
FILE_OR_PATH=/opt/robo/src/robomongo/   # or MainWindow.cpp
CHECKS=clang-analyzer-*,readability-implicit-bool-conversion
# All Checks: https://clang.llvm.org/extra/clang-tidy/checks/list.html

# e.g. $BIN_DIR/run-clang-tidy.py -checks=clang-analyzer-*,readability-implicit-bool-conversion /opt/robo/src/robomongo/
$BIN_DIR/run-clang-tidy.py -checks=$CHECKS $FILE_OR_PATH

echo -e "\n------------------------------------------------"
echo "clang-tidy finished"
echo "Mode                : $BUILD_TYPE"
echo "CLANG_TIDY_BUILD_DIR: $CLANG_TIDY_BUILD_DIR"
echo "Command executed    : $BIN_DIR/run-clang-tidy.py -checks=$CHECKS $FILE_OR_PATH"
echo -e "------------------------------------------------\n"